# esign-qa-mcp-service

电子签名QA造数平台MCP服务 - 简化版本

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动服务
```bash
python main.py
```

### 3. 访问服务
- **API文档**: http://localhost:8003/docs
- **健康检查**: http://localhost:8003/health_check
- **MCP端点**: http://localhost:8003/mcp

## 🛠️ MCP工具 (25个)

### 证书域 (6个)
- `certificate_get_test_account` - 获取测试账号
- `certificate_create` - 创建证书
- `certificate_query_detail` - 查询证书详情
- `certificate_revoke` - 吊销证书
- `certificate_update` - 更新证书
- `certificate_get_user_info` - 获取用户信息

### 签署域 (5个)
- `signing_create_flow` - 创建签署流程
- `signing_add_signer` - 添加签署人
- `signing_start` - 启动签署
- `signing_query_status` - 查询签署状态
- `signing_cancel` - 取消签署

### SaaS域 (5个)
- `saas_register_person_account` - 注册测试个人账号
- `saas_register_company_account` - 注册测试企业账号
- `saas_create_organization` - 创建组织
- `saas_add_member_to_org` - 添加组织成员
- `saas_query_org_info` - 查询组织信息

### 实名域 (5个)
- `identity_create_verification` - 创建实名认证
- `identity_query_verification_status` - 查询认证状态
- `identity_upload_materials` - 上传身份材料
- `identity_verify_bank_card` - 银行卡验证
- `identity_verify_mobile` - 手机号验证

### 意愿域 (4个)
- `intention_create_verification` - 创建意愿验证
- `intention_query_status` - 查询意愿状态
- `intention_confirm` - 确认意愿
- `intention_cancel` - 取消意愿

## 🔧 Cursor MCP 配置

在Cursor中配置MCP服务：

```json
{
  "mcpServers": {
    "esign-qa-mcp": {
      "url": "http://localhost:8003/mcp",
      "type": "sse"
    }
  }
}
```

## 📝 配置说明

- 所有配置从 `.env` 文件读取
- 支持环境切换 (test/mock)
- 响应结果包含请求详情
- 使用官方 FastApiMCP 框架

## ✅ 特性

- ✅ 25个核心业务MCP工具
- ✅ 环境自动切换
- ✅ 统一错误处理
- ✅ 请求详情记录
- ✅ 配置文件驱动
- ✅ 简化代码结构

## 📋 注意事项

- 服务端口: 8003
- 只暴露核心业务功能
- 删除了所有意淫的监控功能
- 使用FastApiMCP框架控制工具展示
