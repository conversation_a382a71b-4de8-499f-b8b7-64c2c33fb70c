#!/usr/bin/env python3
"""
通用工具模块 - 统一HTTP请求处理和配置管理
"""
import json
import requests
import logging
from typing import Dict, Any, Optional
from app.core.config import settings

# 配置日志
logger = logging.getLogger(__name__)

# 删除硬编码配置，改为从settings读取


def get_headers(app_id: str, service_group: Optional[str] = None) -> Dict[str, str]:
    """
    获取请求头
    
    :param app_id: 应用ID
    :param service_group: 服务组
    :return: 请求头字典
    """
    headers = {
        "Content-Type": "application/json",
        "X-Tsign-Open-App-Id": app_id,
        "X-Tsign-Open-Auth-Mode": "simple",
        "filter-result": "false"
    }
    
    if service_group:
        headers["X-Tsign-Service-Group"] = service_group
        
    return headers


def make_http_request(url: str, data: Optional[Dict[str, Any]] = None,
                      headers: Optional[Dict[str, str]] = None,
                      method: str = "POST", timeout: int = 30) -> Dict[str, Any]:
    """
    通用HTTP请求方法
    
    :param url: 请求URL
    :param data: 请求数据
    :param headers: 请求头
    :param method: 请求方法
    :param timeout: 超时时间
    :return: 响应结果
    """
    try:
        # 合并默认请求头
        request_headers = DEFAULT_HEADERS.copy()
        if headers:
            request_headers.update(headers)

        logger.info(f"发起HTTP请求: {method} {url}")
        logger.debug(f"请求头: {request_headers}")
        logger.debug(f"请求数据: {data}")

        if method.upper() == "POST":
            response = requests.post(url, json=data, headers=request_headers, timeout=timeout)
        elif method.upper() == "GET":
            response = requests.get(url, params=data, headers=request_headers, timeout=timeout)
        else:
            raise ValueError(f"不支持的请求方法: {method}")

        # 检查响应状态
        response.raise_for_status()

        # 记录请求日志
        log_request(url, method, request_headers, data, response.status_code, response.text)

        return response.json()

    except requests.exceptions.RequestException as e:
        logger.error(f"HTTP请求失败: {str(e)}")
        error_response = {
            "status": "error",
            "message": f"请求失败: {str(e)}",
            "url": url,
            "method": method
        }
        
        # 记录错误日志
        log_request(url, method, request_headers if 'request_headers' in locals() else {}, 
                   data, None, str(e))
        
        return error_response


def log_request(url: str, method: str, headers: Dict[str, str], 
                input_data: Optional[Dict[str, Any]], status_code: Optional[int], 
                output: str) -> None:
    """
    记录请求日志
    
    :param url: 请求URL
    :param method: 请求方法
    :param headers: 请求头
    :param input_data: 请求数据
    :param status_code: 响应状态码
    :param output: 响应内容
    """
    try:
        log_data = {
            "url": url,
            "method": method,
            "headers": headers,
            "input": input_data,
            "status_code": status_code,
            "output": output,
            "timestamp": str(logger.handlers[0].formatter.formatTime(logging.LogRecord(
                name="", level=0, pathname="", lineno=0, msg="", args=(), exc_info=None
            ))) if logger.handlers else None
        }
        
        # 写入请求记录文件
        with open("请求记录.txt", "a", encoding="utf-8") as f:
            f.write(json.dumps(log_data, ensure_ascii=False, indent=2))
            f.write("\n" + "="*80 + "\n")
            
    except Exception as e:
        logger.error(f"记录请求日志失败: {str(e)}")


def get_test_environment_url(service_type: str = "api") -> str:
    """
    获取测试环境URL
    
    :param service_type: 服务类型 (api, cert, footstone_user)
    :return: 环境URL
    """
    if service_type == "cert":
        return CERT_ENV_URLS["test"]
    elif service_type == "footstone_user":
        return ENV_URLS["footstone_user_url"]
    else:
        return ENV_URLS["test"]


def format_response(status: str, data: Any = None, message: str = "", 
                   extra_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    格式化响应结果
    
    :param status: 状态 (success/error)
    :param data: 响应数据
    :param message: 响应消息
    :param extra_info: 额外信息
    :return: 格式化的响应
    """
    response = {
        "status": status,
        "message": message
    }
    
    if data is not None:
        response["data"] = data
        
    if extra_info:
        response.update(extra_info)
        
    return response


def validate_required_params(params: Dict[str, Any], required_fields: list) -> Optional[str]:
    """
    验证必需参数
    
    :param params: 参数字典
    :param required_fields: 必需字段列表
    :return: 错误消息，如果验证通过则返回None
    """
    missing_fields = []
    for field in required_fields:
        if field not in params or params[field] is None or params[field] == "":
            missing_fields.append(field)
    
    if missing_fields:
        return f"缺少必需参数: {', '.join(missing_fields)}"
    
    return None
