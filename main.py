#!/usr/bin/env python3
"""
主启动文件 - 使用 fastapi_mcp 自动暴露 MCP 工具
"""
import uvicorn
import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi_mcp import FastApiMCP

from app.core.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title=settings.PROJECT_NAME,
        description=settings.PROJECT_DESCRIPTION,
        version=settings.VERSION,
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
    )

    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 包含所有业务域路由
    # 简化路由注册 - 批量加载所有域路由
    routers = [
        ("certificate_controller", "certificate_router", "证书域"),
        ("signing_controller", "signing_router", "签署域"),
        ("saas_controller", "saas_router", "SaaS域"),
        ("identity_controller", "identity_router", "实名域"),
        ("intention_controller", "intention_router", "意愿域"),
        ("platform_controller", "platform_router", "平台功能")
    ]

    for module_name, router_name, domain_name in routers:
        try:
            module = __import__(f"app.mcpController.domains.{module_name}", fromlist=[router_name])
            router = getattr(module, router_name)
            app.include_router(router, prefix=f"{settings.API_V1_STR}")
            logger.info(f"{domain_name}路由加载成功")
        except Exception as e:
            logger.error(f"{domain_name}路由加载失败: {str(e)}")

    # 添加健康检查端点
    @app.get("/health_check")
    async def health_check():
        return {"status": "healthy", "service": settings.PROJECT_NAME}

    # 设置 MCP 服务 - 使用 fastapi_mcp 自动暴露工具
    setup_mcp_service(app)

    return app


def setup_mcp_service(app: FastAPI):
    """设置MCP服务 - 使用官方FastApiMCP框架"""
    try:
        logger.info("🚀 设置MCP服务...")

        # 使用官方FastApiMCP框架，只暴露核心业务功能
        mcp = FastApiMCP(
            app,
            include_tags=["证书域", "签署域", "SaaS域", "实名域", "意愿域"]  # 只暴露业务域，不包含平台功能
        )

        # 挂载MCP服务
        mcp.mount()

        logger.info("✅ MCP服务设置完成 - 使用官方FastApiMCP框架")
        return True

    except Exception as e:
        logger.error(f"❌ MCP服务设置失败: {str(e)}")
        logger.warning("⚠️ 跳过MCP服务设置，继续启动基本服务")
        return None


def main():
    """主函数"""
    try:
        logger.info(f"🚀 启动 {settings.PROJECT_NAME}...")

        # 创建FastAPI应用
        app = create_app()

        # 启动服务器
        port = 8000
        logger.info(f"🌐 服务将在 http://localhost:{port} 启动")
        logger.info(f"📚 API文档: http://localhost:{port}/docs")
        logger.info(f"🔌 MCP端点: http://localhost:{port}/mcp")
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=port,
            log_level="info"
        )

    except KeyboardInterrupt:
        logger.info("👋 服务已停止")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {str(e)}")
        raise


if __name__ == "__main__":
    main()
