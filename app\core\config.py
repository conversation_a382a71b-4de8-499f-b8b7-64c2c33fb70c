"""
应用配置设置 - 造数平台版本
支持多环境配置和自然语言环境切换
"""
from pydantic_settings import BaseSettings
from typing import Optional, Dict, Any
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()  # 默认加载 .env
if os.path.exists('.env_sml'):
    load_dotenv('.env_sml', override=True)  # 如果存在 .env_sml 则覆盖加载

class EnvironmentManager:
    """环境管理器 - 简化版本"""

    def __init__(self):
        self._current_env = "test"

    def detect_environment_from_text(self, text: str) -> str:
        """从自然语言文本中检测环境类型"""
        if not text:
            return self._current_env

        text_lower = text.lower()
        mock_keywords = ["模拟", "mock", "仿真", "虚拟", "假", "模拟环境"]

        for keyword in mock_keywords:
            if keyword in text_lower:
                return "mock"

        return "test"  # 默认测试环境

    def set_environment(self, env: str):
        """设置当前环境"""
        if env in ["test", "mock"]:
            self._current_env = env

    @property
    def current_environment(self) -> str:
        """获取当前环境"""
        return self._current_env

# 全局环境管理器
env_manager = EnvironmentManager()

class Settings(BaseSettings):
    """应用配置类"""

    # 项目基本信息
    PROJECT_NAME: str = "esign-qa-mcp-platform"
    PROJECT_DESCRIPTION: str = "电子签名QA造数平台MCP服务"
    VERSION: str = "2.0.0"

    # 服务配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    API_V1_STR: str = "/api/v1"

    # MCP配置
    MCP_NAME: str = "E-Sign QA Data Platform"
    MCP_DESCRIPTION: str = "专业的电子签名QA造数平台，支持多业务域和环境切换"

    # 数据库配置
    DATABASE_URL: str = "sqlite:///./platform.db"

    # 环境管理
    DEFAULT_ENVIRONMENT: str = "test"

    # 从环境变量读取配置
    # 基础URL配置
    BASE_URL: str = os.getenv("base_url", "http://in-test-openapi.tsign.cn")
    FOOTSTONE_API_URL: str = os.getenv("footstone_api_url", "http://in-test-openapi.tsign.cn")
    FOOTSTONE_WILL: str = os.getenv("footstone_will", "http://footstone-will.testk8s.tsign.cn")
    FOOTSTONE_WILLAUTH: str = os.getenv("footstone_willauth", "http://willauth-service.testk8s.tsign.cn")
    FOOTSTONE_TEMPLATE_URL: str = os.getenv("footstone_template_url", "http://footstone-doc.testk8s.tsign.cn")
    FOOTSTONE_USER_URL: str = os.getenv("footstone_user_url", "http://footstone-user-api.testk8s.tsign.cn")
    FOOTSTONE_DOC_URL: str = os.getenv("footstone_doc_url", "http://footstone-doc.testk8s.tsign.cn")
    OPEN_PLATFORM_URL: str = os.getenv("open_platform_url", "http://open-platform-service.testk8s.tsign.cn")
    REALNAME_URL: str = os.getenv("realname_url", "http://footstone-identity.testk8s.tsign.cn")
    FLOW_MANAGER_URL: str = os.getenv("flow_manager_url", "http://flow-manager.testk8s.tsign.cn")
    SEAL_MANAGER_URL: str = os.getenv("seal_manager_url", "http://seal-manager.testk8s.tsign.cn")
    ACCOUNT_URL: str = os.getenv("account_url", "http://footstone-user-api.testk8s.tsign.cn")

    # 应用ID配置
    APP_ID: str = os.getenv("app_id", "**********")
    APPID1: str = os.getenv("appid1", "**********")
    APPID2: str = os.getenv("appid2", "**********")
    APPID_CERT: str = os.getenv("appid_cert", "**********")
    APPID_CERT2: str = os.getenv("appid_cert2", "**********")

    # 环境配置
    ENV: str = os.getenv("env", "test")
    ENV_CODE: str = os.getenv("envCode", "testvpc")
    IS_MOCK: bool = os.getenv("is_mock", "false").lower() == "true"

    # 请求头配置
    X_TSIGN_SERVICE_GROUP: str = os.getenv("X-Tsign-Service-Group", "DEFAULT")
    BZQ_APP_ID: str = os.getenv("BZQ-App-Id", "**********")
    X_TSIGN_OPEN_APP_ID: str = os.getenv("X-Tsign-Open-App-Id", "**********")
    X_TSIGN_OPEN_TENANT_ID: str = os.getenv("X-Tsign-Open-Tenant-Id", "b0f99abbc3cd4d63a5a2a84c452e52d6")

    # 请求配置
    REQUEST_TIMEOUT: int = 30
    LOG_REQUESTS: bool = True

    # 日志配置
    LOG_LEVEL: str = "INFO"

    # 平台功能配置
    ENABLE_AUTO_CODE_GENERATION: bool = True
    AUTO_CODE_TRIGGER_KEYWORD: str = "接入mcp"

    def get_api_config(self, env: str = None) -> Dict[str, Any]:
        """获取API配置"""
        current_env = env or env_manager.current_environment

        if current_env == "mock" or self.IS_MOCK:
            return {
                "base_url": os.getenv("mock_base_url", "http://mock-openapi.tsign.cn"),
                "footstone_api_url": os.getenv("mock_footstone_api_url", "http://mock-openapi.tsign.cn"),
                "app_id": os.getenv("mock_app_id", "mock_app_id"),
                "cert_app_id": os.getenv("mock_cert_app_id", "mock_cert_id")
            }
        else:
            return {
                "base_url": self.BASE_URL,
                "footstone_api_url": self.FOOTSTONE_API_URL,
                "footstone_will": self.FOOTSTONE_WILL,
                "footstone_willauth": self.FOOTSTONE_WILLAUTH,
                "footstone_template_url": self.FOOTSTONE_TEMPLATE_URL,
                "footstone_user_url": self.FOOTSTONE_USER_URL,
                "footstone_doc_url": self.FOOTSTONE_DOC_URL,
                "open_platform_url": self.OPEN_PLATFORM_URL,
                "realname_url": self.REALNAME_URL,
                "flow_manager_url": self.FLOW_MANAGER_URL,
                "seal_manager_url": self.SEAL_MANAGER_URL,
                "account_url": self.ACCOUNT_URL,
                "app_id": self.APP_ID,
                "cert_app_id": self.APPID_CERT
            }

    class Config:
        env_file = [".env", ".env_sml"]
        env_file_encoding = "utf-8"
        extra = "ignore"  # 忽略额外的环境变量

# 创建全局配置实例
settings = Settings()
